import bambulabs_api as bl
import time
import json

IP = '**************'  # Reemplázalo con la IP de tu impresora
ACCESS_CODE = '40541866'  # Código de acceso
SERIAL = '03919C452109217'  # Número de serie

print("Conectando a la impresora...")
printer = bl.Printer(IP, ACCESS_CODE, SERIAL)
printer.mqtt_start()

print("Esperando conexión MQTT...")
time.sleep(5)  # Dar más tiempo para que se establezca la conexión

# Intentar obtener el estado varias veces
for i in range(3):
    try:
        status = printer.get_state()
        print(f'Estado de la impresora: {status}')

        printer.turn_light_off()

        print(json.dumps(
            printer.mqtt_dump(),
            sort_keys=True,
            indent=2
        ))

        break
    except Exception as e:
        print(f"Intento {i+1}: Error al obtener estado - {e}")
        if i < 2:  # Si no es el último intento
            print("Esperando 3 segundos antes del siguiente intento...")
            time.sleep(3)

print("Desconectando...")
printer.mqtt_stop()
